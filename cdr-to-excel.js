const fs = require('fs');
const XLSX = require('xlsx');

class CDRExcelExporter {
    constructor() {
        this.workbook = XLSX.utils.book_new();
        this.cdrData = [];
        this.stats = {};
    }

    // 載入CDR數據
    loadCDRData() {
        try {
            // 讀取JSON數據
            const jsonData = JSON.parse(fs.readFileSync('CDR-完整數據.json', 'utf-8'));
            this.cdrData = jsonData.records;
            this.stats = jsonData.statistics;
            console.log(`載入 ${this.cdrData.length} 條CDR記錄`);
            return true;
        } catch (error) {
            console.error('載入CDR數據失敗:', error.message);
            return false;
        }
    }

    // 創建概覽工作表
    createSummarySheet() {
        const summaryData = [
            ['CDR 通話詳細記錄分析報告', '', '', ''],
            ['', '', '', ''],
            ['📊 基本統計信息', '', '', ''],
            ['項目', '數值', '單位', '備註'],
            ['總通話數量', this.stats.totalCalls, '通', ''],
            ['總通話時長', Math.floor(this.stats.totalDuration / 60), '分鐘', `${this.stats.totalDuration % 60}秒`],
            ['總通話費用', this.stats.totalCost.toFixed(2), '元', ''],
            ['平均通話時長', this.stats.averageDuration, '秒', ''],
            ['平均通話費用', this.stats.averageCost, '元', ''],
            ['數據時間範圍', this.stats.startDate, '~', this.stats.endDate],
            ['', '', '', ''],
            ['📞 呼叫結果統計', '', '', ''],
            ['結果類型', '數量', '百分比', ''],
        ];

        // 添加呼叫結果數據
        Object.entries(this.stats.callResults).forEach(([result, count]) => {
            const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1) + '%';
            summaryData.push([result, count, percentage, '']);
        });

        summaryData.push(['', '', '', '']);
        summaryData.push(['⏱️ 通話時長分布', '', '', '']);
        summaryData.push(['時長範圍', '數量', '百分比', '']);

        // 添加時長分布數據
        Object.entries(this.stats.durationDistribution).forEach(([range, count]) => {
            const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1) + '%';
            summaryData.push([range, count, percentage, '']);
        });

        const worksheet = XLSX.utils.aoa_to_sheet(summaryData);
        
        // 設置列寬
        worksheet['!cols'] = [
            { width: 25 },
            { width: 15 },
            { width: 15 },
            { width: 20 }
        ];

        // 設置樣式範圍
        worksheet['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 3 } }
        ];

        XLSX.utils.book_append_sheet(this.workbook, worksheet, '概覽統計');
    }

    // 創建坐席統計工作表
    createAgentSheet() {
        const agentData = [
            ['👥 坐席統計報告', '', '', '', '', '', ''],
            ['', '', '', '', '', '', ''],
            ['坐席工號', '通話數量', '通話時長(分)', '通話時長(秒)', '通話費用(元)', '平均時長(秒)', '工作量占比']
        ];

        // 添加坐席數據
        Object.entries(this.stats.agentStats)
            .sort(([,a], [,b]) => b.calls - a.calls)
            .forEach(([agent, stats]) => {
                const avgDuration = Math.round(stats.duration / stats.calls);
                const percentage = ((stats.calls / this.stats.totalCalls) * 100).toFixed(1) + '%';
                const durationMinutes = Math.floor(stats.duration / 60);
                
                agentData.push([
                    agent,
                    stats.calls,
                    `${durationMinutes}分${stats.duration % 60}秒`,
                    stats.duration,
                    stats.cost.toFixed(2),
                    avgDuration,
                    percentage
                ]);
            });

        // 添加總計行
        agentData.push(['', '', '', '', '', '', '']);
        agentData.push([
            '總計',
            this.stats.totalCalls,
            `${Math.floor(this.stats.totalDuration / 60)}分${this.stats.totalDuration % 60}秒`,
            this.stats.totalDuration,
            this.stats.totalCost.toFixed(2),
            this.stats.averageDuration,
            '100.0%'
        ]);

        const worksheet = XLSX.utils.aoa_to_sheet(agentData);
        
        // 設置列寬
        worksheet['!cols'] = [
            { width: 20 },
            { width: 12 },
            { width: 15 },
            { width: 15 },
            { width: 15 },
            { width: 15 },
            { width: 12 }
        ];

        // 合併標題
        worksheet['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 6 } }
        ];

        XLSX.utils.book_append_sheet(this.workbook, worksheet, '坐席統計');
    }

    // 創建詳細通話記錄工作表
    createDetailSheet() {
        const detailData = [
            ['📋 詳細通話記錄', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            [
                '行號', '坐席工號', '任務', '業務類型', '創建時間', '開始時間', '結束時間',
                '主叫號碼', '被叫號碼', '通話時長(秒)', '費用(元)', '掛斷原因', '呼叫結果',
                '類型', '振鈴時長(秒)', '按鍵信息', '省份', '城市', '運營商', '錯誤信息'
            ]
        ];

        // 添加詳細記錄
        this.cdrData.forEach(record => {
            detailData.push([
                record.rowNumber,
                record.agentId,
                record.task,
                record.businessType,
                record.createTime,
                record.startTime,
                record.endTime,
                record.callerNumber,
                record.calleeNumber,
                record.duration,
                record.cost,
                record.hangupReason,
                record.callResult,
                record.type,
                record.ringDuration,
                record.keyInfo,
                record.province,
                record.city,
                record.operator,
                record.chineseError
            ]);
        });

        const worksheet = XLSX.utils.aoa_to_sheet(detailData);
        
        // 設置列寬
        worksheet['!cols'] = [
            { width: 8 },   // 行號
            { width: 15 },  // 坐席工號
            { width: 10 },  // 任務
            { width: 12 },  // 業務類型
            { width: 20 },  // 創建時間
            { width: 20 },  // 開始時間
            { width: 20 },  // 結束時間
            { width: 12 },  // 主叫號碼
            { width: 12 },  // 被叫號碼
            { width: 12 },  // 通話時長
            { width: 10 },  // 費用
            { width: 15 },  // 掛斷原因
            { width: 15 },  // 呼叫結果
            { width: 8 },   // 類型
            { width: 12 },  // 振鈴時長
            { width: 10 },  // 按鍵信息
            { width: 8 },   // 省份
            { width: 8 },   // 城市
            { width: 10 },  // 運營商
            { width: 15 }   // 錯誤信息
        ];

        // 合併標題
        worksheet['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 19 } }
        ];

        XLSX.utils.book_append_sheet(this.workbook, worksheet, '詳細記錄');
    }

    // 創建數據透視表工作表
    createPivotSheet() {
        const pivotData = [
            ['📊 數據透視分析', '', '', '', ''],
            ['', '', '', '', ''],
            ['按日期統計', '', '', '', ''],
            ['日期', '通話數量', '通話時長(分)', '通話費用(元)', '平均時長(秒)']
        ];

        // 按日期分組統計
        const dateStats = {};
        this.cdrData.forEach(record => {
            const date = record.startTime.split(' ')[0]; // 提取日期部分
            if (!dateStats[date]) {
                dateStats[date] = { calls: 0, duration: 0, cost: 0 };
            }
            dateStats[date].calls++;
            dateStats[date].duration += record.duration;
            dateStats[date].cost += record.cost;
        });

        // 添加日期統計數據
        Object.entries(dateStats)
            .sort(([a], [b]) => a.localeCompare(b))
            .forEach(([date, stats]) => {
                const avgDuration = Math.round(stats.duration / stats.calls);
                const durationMinutes = Math.floor(stats.duration / 60);
                
                pivotData.push([
                    date,
                    stats.calls,
                    durationMinutes,
                    stats.cost.toFixed(2),
                    avgDuration
                ]);
            });

        pivotData.push(['', '', '', '', '']);
        pivotData.push(['按小時統計', '', '', '', '']);
        pivotData.push(['小時', '通話數量', '通話時長(分)', '通話費用(元)', '平均時長(秒)']);

        // 按小時分組統計
        const hourStats = {};
        this.cdrData.forEach(record => {
            const hour = record.startTime.split(' ')[1].split(':')[0]; // 提取小時
            if (!hourStats[hour]) {
                hourStats[hour] = { calls: 0, duration: 0, cost: 0 };
            }
            hourStats[hour].calls++;
            hourStats[hour].duration += record.duration;
            hourStats[hour].cost += record.cost;
        });

        // 添加小時統計數據
        Object.entries(hourStats)
            .sort(([a], [b]) => parseInt(a) - parseInt(b))
            .forEach(([hour, stats]) => {
                const avgDuration = Math.round(stats.duration / stats.calls);
                const durationMinutes = Math.floor(stats.duration / 60);
                
                pivotData.push([
                    `${hour}:00`,
                    stats.calls,
                    durationMinutes,
                    stats.cost.toFixed(2),
                    avgDuration
                ]);
            });

        const worksheet = XLSX.utils.aoa_to_sheet(pivotData);
        
        // 設置列寬
        worksheet['!cols'] = [
            { width: 15 },
            { width: 12 },
            { width: 15 },
            { width: 15 },
            { width: 15 }
        ];

        // 合併標題
        worksheet['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } }
        ];

        XLSX.utils.book_append_sheet(this.workbook, worksheet, '數據透視');
    }

    // 生成Excel文件
    generateExcel(filename = 'CDR-分析報告.xlsx') {
        if (!this.loadCDRData()) {
            return false;
        }

        console.log('正在生成Excel工作表...');
        
        // 創建各個工作表
        this.createSummarySheet();
        this.createAgentSheet();
        this.createDetailSheet();
        this.createPivotSheet();

        try {
            // 寫入Excel文件
            XLSX.writeFile(this.workbook, filename);
            console.log(`✅ Excel文件已生成: ${filename}`);
            return true;
        } catch (error) {
            console.error('生成Excel文件失敗:', error.message);
            return false;
        }
    }
}

// 主程序
function main() {
    console.log('開始轉換CDR數據為Excel格式...');

    const exporter = new CDRExcelExporter();
    const outputFile = process.argv[2] || 'CDR-分析報告.xlsx';

    if (exporter.generateExcel(outputFile)) {
        console.log('\n🎉 CDR數據已成功轉換為Excel格式！');
        console.log('📁 生成的文件包含以下工作表：');
        console.log('   • 概覽統計 - 基本統計信息和分布圖表');
        console.log('   • 坐席統計 - 各坐席詳細工作統計');
        console.log('   • 詳細記錄 - 完整的通話記錄數據');
        console.log('   • 數據透視 - 按日期和時間的透視分析');
    } else {
        console.error('❌ Excel文件生成失敗');
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    main();
}

module.exports = CDRExcelExporter;
