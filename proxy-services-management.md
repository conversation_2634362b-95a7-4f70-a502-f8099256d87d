# 代理服务管理指南

## 🌐 已部署的代理服务概览

### Shadowsocks服务器
| 端口 | IP地址 | 状态 | 用途 |
|------|--------|------|------|
| 28889 | ************ | 运行中 | 主要SS服务 |
| 30001 | ************ | 运行中 | SS服务1 |
| 30002 | ************* | 运行中 | SS服务2 |
| 30003 | ************* | 运行中 | SS服务3 |

### SOCKS5代理服务器
| 端口 | IP地址 | 认证信息 | 状态 |
|------|--------|----------|------|
| 7777 | ************ | user1:pass1 | 运行中 |
| 8001 | ************ | user2:pass2 | 运行中 |
| 8002 | ************* | user3:pass3 | 运行中 |
| 8003 | ************* | user4:pass4 | 运行中 |

## 🔍 正确的代理服务测试方法

### ❌ 错误的测试方法
```bash
# 不要用telnet测试代理端口
telnet localhost 28889  # 这样测试Shadowsocks是无效的
telnet localhost 7777   # 这样测试SOCKS5也是无效的
```

**为什么telnet无效？**
- Shadowsocks使用加密协议，telnet无法正确握手
- SOCKS5需要特定的协议握手过程
- 代理协议不是简单的TCP连接

### ✅ 正确的测试方法

#### 1. 检查端口监听状态
```bash
# 检查特定端口
netstat -tlnp | grep 28889
netstat -tlnp | grep 7777

# 检查所有代理端口
for port in 28889 30001 30002 30003 7777 8001 8002 8003; do
    echo "=== 检查端口 $port ==="
    netstat -tlnp | grep $port
    echo ""
done

# 使用ss命令（更现代）
ss -tlnp | grep 28889
```

#### 2. 检查进程状态
```bash
# 检查Shadowsocks进程
ps aux | grep shadowsocks
ps aux | grep ss-server

# 检查SOCKS5进程
ps aux | grep socks
ps aux | grep dante
ps aux | grep 3proxy

# 检查所有相关进程
ps aux | grep -E "(shadowsocks|socks|dante|3proxy)"
```

#### 3. 使用实际代理客户端测试

**Shadowsocks测试：**
```bash
# 使用ss-local客户端测试
ss-local -s ************ -p 28889 -k "your_password" -m aes-256-gcm -l 1080

# 使用curl通过本地代理测试
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip

# 使用Clash配置测试
# 在Clash配置中添加Shadowsocks节点，然后测试连接
```

**SOCKS5测试：**
```bash
# 直接测试SOCKS5代理
curl --socks5 user1:pass1@************:7777 http://httpbin.org/ip

# 测试无认证的SOCKS5（如果有）
curl --socks5 ************:7777 http://httpbin.org/ip

# 使用proxychains测试
echo "socks5 ************ 7777 user1 pass1" >> /etc/proxychains.conf
proxychains curl http://httpbin.org/ip
```

## 🛠️ 服务管理命令

### 启动/停止服务

**Shadowsocks服务：**
```bash
# 如果使用systemd管理
systemctl start shadowsocks-libev@28889
systemctl stop shadowsocks-libev@28889
systemctl restart shadowsocks-libev@28889
systemctl status shadowsocks-libev@28889

# 手动启动（示例）
ss-server -s 0.0.0.0 -p 28889 -k "password" -m aes-256-gcm -d start
```

**SOCKS5服务：**
```bash
# 如果使用dante-server
systemctl start danted
systemctl stop danted
systemctl restart danted
systemctl status danted

# 如果使用3proxy
systemctl start 3proxy
systemctl stop 3proxy
systemctl restart 3proxy
systemctl status 3proxy
```

### 配置文件位置

**Shadowsocks配置：**
```bash
# 常见配置文件位置
/etc/shadowsocks-libev/config.json
/etc/shadowsocks/config.json
/usr/local/etc/shadowsocks-libev/config.json
```

**SOCKS5配置：**
```bash
# Dante配置
/etc/danted.conf

# 3proxy配置
/etc/3proxy/3proxy.cfg
```

## 📊 监控与维护

### 实时监控脚本
```bash
#!/bin/bash
# proxy-monitor.sh

echo "=== 代理服务监控报告 $(date) ==="
echo ""

# 检查端口监听
echo "=== 端口监听状态 ==="
for port in 28889 30001 30002 30003 7777 8001 8002 8003; do
    status=$(netstat -tlnp | grep ":$port ")
    if [ -n "$status" ]; then
        echo "✅ 端口 $port: 正在监听"
    else
        echo "❌ 端口 $port: 未监听"
    fi
done
echo ""

# 检查进程状态
echo "=== 进程状态 ==="
ps aux | grep -E "(shadowsocks|socks|dante|3proxy)" | grep -v grep
echo ""

# 检查系统资源
echo "=== 系统资源 ==="
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
echo "内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
echo ""

# 检查网络连接数
echo "=== 网络连接统计 ==="
echo "总连接数: $(netstat -an | wc -l)"
echo "ESTABLISHED连接: $(netstat -an | grep ESTABLISHED | wc -l)"
echo ""
```

### 性能测试脚本
```bash
#!/bin/bash
# proxy-test.sh

echo "=== 代理服务功能测试 ==="

# 测试SOCKS5代理
test_socks5() {
    local ip=$1
    local port=$2
    local user=$3
    local pass=$4
    
    echo "测试SOCKS5 ${ip}:${port}"
    result=$(timeout 10 curl -s --socks5 ${user}:${pass}@${ip}:${port} http://httpbin.org/ip 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "✅ SOCKS5 ${ip}:${port} 工作正常"
        echo "   返回IP: $(echo $result | jq -r '.origin' 2>/dev/null || echo $result)"
    else
        echo "❌ SOCKS5 ${ip}:${port} 连接失败"
    fi
    echo ""
}

# 测试各个SOCKS5代理
test_socks5 "************" "7777" "user1" "pass1"
test_socks5 "************" "8001" "user2" "pass2"
test_socks5 "*************" "8002" "user3" "pass3"
test_socks5 "*************" "8003" "user4" "pass4"
```

## 🚨 故障排除指南

### 常见问题诊断

**1. 端口无法监听**
```bash
# 检查端口是否被占用
lsof -i :28889

# 检查防火墙设置
ufw status
iptables -L

# 检查SELinux（如果启用）
sestatus
```

**2. 服务无法启动**
```bash
# 查看服务日志
journalctl -u shadowsocks-libev@28889 -f
journalctl -u danted -f

# 检查配置文件语法
ss-server -c /etc/shadowsocks-libev/config.json -t
```

**3. 连接超时或拒绝**
```bash
# 检查网络连通性
ping ************
telnet ************ 22

# 检查路由
traceroute ************

# 检查DNS解析
nslookup srv211241698.host
```

### 日志分析
```bash
# 查看系统日志中的代理相关信息
grep -i "shadowsocks\|socks\|proxy" /var/log/syslog

# 查看认证日志
grep -i "proxy\|socks" /var/log/auth.log

# 实时监控日志
tail -f /var/log/syslog | grep -i proxy
```

## 📋 维护检查清单

### 日常检查（每天）
- [ ] 检查所有代理端口监听状态
- [ ] 检查进程运行状态
- [ ] 监控系统资源使用
- [ ] 检查连接数和流量

### 周期检查（每周）
- [ ] 测试所有代理服务功能
- [ ] 检查日志中的异常信息
- [ ] 更新服务配置（如需要）
- [ ] 备份配置文件

### 安全检查（每月）
- [ ] 更换代理密码
- [ ] 检查访问日志
- [ ] 更新系统和软件
- [ ] 检查防火墙规则

---
**最后更新**: 2025-07-15
**维护说明**: 定期执行监控脚本，及时发现和解决问题
