<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDR 新數據分析報告 - 包含失敗通話</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .alert {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #2d3436;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            border-left: 5px solid #e17055;
            font-weight: bold;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transform: translateY(0);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #fab1a0 0%, #e17055 100%);
        }
        
        .stat-card h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .stat-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .unit {
            font-size: 1em;
            opacity: 0.8;
        }
        
        .chart-section {
            margin-bottom: 40px;
        }
        
        .chart-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }
        
        .chart-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 2px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .table-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.5em;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .success-rate {
            color: #00b894;
            font-weight: bold;
        }
        
        .failure-rate {
            color: #e17055;
            font-weight: bold;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 CDR 新數據分析報告</h1>
            <p>包含成功與失敗通話的完整分析</p>
            <p>數據時間範圍: 2025/7/21 ~ 2025/7/25</p>
        </div>
        
        <div class="alert">
            ⚠️ 重要發現：此數據集包含大量失敗通話記錄，成功率僅30%，需要重點關注通話品質問題！
        </div>
        
        <div class="content">
            <!-- 基本統計卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>總通話嘗試</h3>
                    <div class="value">7,687</div>
                    <div class="unit">通</div>
                </div>
                <div class="stat-card warning">
                    <h3>成功通話</h3>
                    <div class="value">2,306</div>
                    <div class="unit">通 (30%)</div>
                </div>
                <div class="stat-card warning">
                    <h3>失敗通話</h3>
                    <div class="value">5,381</div>
                    <div class="unit">通 (70%)</div>
                </div>
                <div class="stat-card">
                    <h3>總通話時長</h3>
                    <div class="value">974</div>
                    <div class="unit">分鐘</div>
                </div>
                <div class="stat-card">
                    <h3>總通話費用</h3>
                    <div class="value">466.20</div>
                    <div class="unit">元</div>
                </div>
                <div class="stat-card warning">
                    <h3>平均通話時長</h3>
                    <div class="value">8</div>
                    <div class="unit">秒</div>
                </div>
            </div>
            
            <!-- 坐席統計表格 -->
            <div class="table-container">
                <div class="table-title">👥 坐席統計（包含失敗通話）</div>
                <table>
                    <thead>
                        <tr>
                            <th>坐席工號</th>
                            <th>總嘗試數</th>
                            <th>成功率</th>
                            <th>通話時長</th>
                            <th>通話費用</th>
                            <th>平均時長</th>
                            <th>工作量占比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>6102012</td>
                            <td>1,422通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>110分21秒</td>
                            <td>61.74元</td>
                            <td>5秒</td>
                            <td>18.5%<div class="progress-bar"><div class="progress-fill" style="width: 18.5%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102006</td>
                            <td>1,195通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>114分22秒</td>
                            <td>68.58元</td>
                            <td>6秒</td>
                            <td>15.5%<div class="progress-bar"><div class="progress-fill" style="width: 15.5%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102010</td>
                            <td>1,133通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>131分48秒</td>
                            <td>74.88元</td>
                            <td>7秒</td>
                            <td>14.7%<div class="progress-bar"><div class="progress-fill" style="width: 14.7%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102009</td>
                            <td>1,072通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>181分22秒</td>
                            <td>75.42元</td>
                            <td>10秒</td>
                            <td>13.9%<div class="progress-bar"><div class="progress-fill" style="width: 13.9%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102013</td>
                            <td>944通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>137分48秒</td>
                            <td>59.76元</td>
                            <td>9秒</td>
                            <td>12.3%<div class="progress-bar"><div class="progress-fill" style="width: 12.3%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102008</td>
                            <td>848通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>107分35秒</td>
                            <td>56.34元</td>
                            <td>8秒</td>
                            <td>11.0%<div class="progress-bar"><div class="progress-fill" style="width: 11.0%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102007</td>
                            <td>614通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>144分27秒</td>
                            <td>47.16元</td>
                            <td>14秒</td>
                            <td>8.0%<div class="progress-bar"><div class="progress-fill" style="width: 8.0%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102011</td>
                            <td>459通</td>
                            <td><span class="failure-rate">低成功率</span></td>
                            <td>46分41秒</td>
                            <td>22.32元</td>
                            <td>6秒</td>
                            <td>6.0%<div class="progress-bar"><div class="progress-fill" style="width: 6.0%"></div></div></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 圖表區域 -->
            <div class="chart-section">
                <h2 class="chart-title">📞 呼叫結果分析</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="resultChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">📴 失敗原因分析</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="failureChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">👥 坐席工作量分布</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="agentChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">⏱️ 通話時長分布</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="durationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>📄 報告生成時間: <span id="currentTime"></span></p>
            <p>🔍 數據來源: CDR-20250725163335.csv</p>
            <p>⚠️ 建議：需要立即調查通話失敗原因並改善系統穩定性</p>
        </div>
    </div>

    <script>
        // 設置當前時間
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-TW');
        
        // 呼叫結果圖表
        const resultCtx = document.getElementById('resultChart').getContext('2d');
        new Chart(resultCtx, {
            type: 'doughnut',
            data: {
                labels: ['坐席掛斷', '客戶未接通', '成功-客戶掛斷', '成功-坐席掛斷', '未振鈴未接通', '內部呼損'],
                datasets: [{
                    data: [3134, 2018, 1279, 1054, 184, 18],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(230, 126, 34, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(149, 165, 166, 0.8)'
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(230, 126, 34, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(149, 165, 166, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        
        // 失敗原因圖表
        const failureCtx = document.getElementById('failureChart').getContext('2d');
        new Chart(failureCtx, {
            type: 'bar',
            data: {
                labels: ['4xx消息(303)', '成功', '4xx消息(313)', '終止呼叫(487)', '5xx消息(315)', '5xx消息(316)', '振鈴超時', '其他'],
                datasets: [{
                    label: '數量',
                    data: [5111, 2306, 191, 27, 23, 18, 9, 2],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(230, 126, 34, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(149, 165, 166, 0.8)',
                        'rgba(127, 140, 141, 0.8)'
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(230, 126, 34, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(149, 165, 166, 1)',
                        'rgba(127, 140, 141, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // 坐席工作量圖表
        const agentCtx = document.getElementById('agentChart').getContext('2d');
        new Chart(agentCtx, {
            type: 'bar',
            data: {
                labels: ['6102012', '6102006', '6102010', '6102009', '6102013', '6102008', '6102007', '6102011'],
                datasets: [{
                    label: '通話嘗試數',
                    data: [1422, 1195, 1133, 1072, 944, 848, 614, 459],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(230, 126, 34, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(149, 165, 166, 0.8)',
                        'rgba(127, 140, 141, 0.8)'
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(230, 126, 34, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(149, 165, 166, 1)',
                        'rgba(127, 140, 141, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // 通話時長分布圖表
        const durationCtx = document.getElementById('durationChart').getContext('2d');
        new Chart(durationCtx, {
            type: 'doughnut',
            data: {
                labels: ['0-30秒', '31-60秒', '61-120秒', '121-300秒', '300秒以上'],
                datasets: [{
                    data: [7124, 378, 153, 29, 3],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(155, 89, 182, 0.8)'
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(155, 89, 182, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
