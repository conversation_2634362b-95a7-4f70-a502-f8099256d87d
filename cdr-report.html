<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDR 通話詳細記錄分析報告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transform: translateY(0);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .stat-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .unit {
            font-size: 1em;
            opacity: 0.8;
        }
        
        .chart-section {
            margin-bottom: 40px;
        }
        
        .chart-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }
        
        .chart-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 2px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .table-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.5em;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 CDR 通話詳細記錄分析報告</h1>
            <p>數據時間範圍: 2025/7/21 ~ 2025/7/25</p>
        </div>
        
        <div class="content">
            <!-- 基本統計卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>總通話數量</h3>
                    <div class="value">589</div>
                    <div class="unit">通</div>
                </div>
                <div class="stat-card">
                    <h3>總通話時長</h3>
                    <div class="value">579</div>
                    <div class="unit">分鐘</div>
                </div>
                <div class="stat-card">
                    <h3>總通話費用</h3>
                    <div class="value">148.86</div>
                    <div class="unit">元</div>
                </div>
                <div class="stat-card">
                    <h3>平均通話時長</h3>
                    <div class="value">59</div>
                    <div class="unit">秒</div>
                </div>
            </div>
            
            <!-- 坐席統計表格 -->
            <div class="table-container">
                <div class="table-title">👥 坐席統計</div>
                <table>
                    <thead>
                        <tr>
                            <th>坐席工號</th>
                            <th>通話數量</th>
                            <th>通話時長</th>
                            <th>通話費用</th>
                            <th>平均時長</th>
                            <th>工作量占比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>6102009</td>
                            <td>128通</td>
                            <td>121分0秒</td>
                            <td>31.14元</td>
                            <td>57秒</td>
                            <td>21.7%<div class="progress-bar"><div class="progress-fill" style="width: 21.7%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102013</td>
                            <td>95通</td>
                            <td>85分51秒</td>
                            <td>22.32元</td>
                            <td>54秒</td>
                            <td>16.1%<div class="progress-bar"><div class="progress-fill" style="width: 16.1%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102007</td>
                            <td>89通</td>
                            <td>114分54秒</td>
                            <td>28.08元</td>
                            <td>77秒</td>
                            <td>15.1%<div class="progress-bar"><div class="progress-fill" style="width: 15.1%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102012</td>
                            <td>67通</td>
                            <td>64分27秒</td>
                            <td>16.38元</td>
                            <td>58秒</td>
                            <td>11.4%<div class="progress-bar"><div class="progress-fill" style="width: 11.4%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102008</td>
                            <td>67通</td>
                            <td>57分10秒</td>
                            <td>15.12元</td>
                            <td>51秒</td>
                            <td>11.4%<div class="progress-bar"><div class="progress-fill" style="width: 11.4%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102010</td>
                            <td>64通</td>
                            <td>70分54秒</td>
                            <td>18.36元</td>
                            <td>66秒</td>
                            <td>10.9%<div class="progress-bar"><div class="progress-fill" style="width: 10.9%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102006</td>
                            <td>52通</td>
                            <td>37分3秒</td>
                            <td>10.44元</td>
                            <td>43秒</td>
                            <td>8.8%<div class="progress-bar"><div class="progress-fill" style="width: 8.8%"></div></div></td>
                        </tr>
                        <tr>
                            <td>6102011</td>
                            <td>27通</td>
                            <td>27分41秒</td>
                            <td>7.02元</td>
                            <td>62秒</td>
                            <td>4.6%<div class="progress-bar"><div class="progress-fill" style="width: 4.6%"></div></div></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 圖表區域 -->
            <div class="chart-section">
                <h2 class="chart-title">📈 坐席工作量分布</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="agentChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">⏱️ 通話時長分布</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="durationChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">📞 呼叫結果統計</h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="resultChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>📄 報告生成時間: <span id="currentTime"></span></p>
            <p>🔍 數據來源: CDR-20250725161920.csv</p>
        </div>
    </div>

    <script>
        // 設置當前時間
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-TW');
        
        // 坐席工作量圖表
        const agentCtx = document.getElementById('agentChart').getContext('2d');
        new Chart(agentCtx, {
            type: 'bar',
            data: {
                labels: ['6102009', '6102013', '6102007', '6102012', '6102008', '6102010', '6102006', '6102011'],
                datasets: [{
                    label: '通話數量',
                    data: [128, 95, 89, 67, 67, 64, 52, 27],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)',
                        'rgba(199, 199, 199, 0.8)',
                        'rgba(83, 102, 255, 0.8)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)',
                        'rgba(83, 102, 255, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // 通話時長分布圖表
        const durationCtx = document.getElementById('durationChart').getContext('2d');
        new Chart(durationCtx, {
            type: 'doughnut',
            data: {
                labels: ['0-30秒', '31-60秒', '61-120秒', '121-300秒', '300秒以上'],
                datasets: [{
                    data: [31, 375, 151, 29, 3],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        
        // 呼叫結果圖表
        const resultCtx = document.getElementById('resultChart').getContext('2d');
        new Chart(resultCtx, {
            type: 'pie',
            data: {
                labels: ['坐席掛斷', '客戶掛斷'],
                datasets: [{
                    data: [327, 262],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
