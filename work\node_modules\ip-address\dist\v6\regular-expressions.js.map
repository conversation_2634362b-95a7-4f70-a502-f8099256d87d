{"version": 3, "file": "regular-expressions.js", "sourceRoot": "", "sources": ["../../src/v6/regular-expressions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,2CAAqC;AAErC,SAAgB,kBAAkB,CAAC,aAAuB;IACxD,OAAO,IAAA,oBAAO,EAAC,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAClD,CAAC;AAFD,gDAEC;AAED,SAAgB,QAAQ,CAAC,KAAa;IACpC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACpB,OAAO,IAAA,oBAAO,EAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACtD;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAND,4BAMC;AAEY,QAAA,gBAAgB,GAAG,eAAe,CAAC;AAEhD,SAAgB,uBAAuB,CAAC,MAAgB;IACtD,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;QAC1B,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEzC,IAAI,YAAY,KAAK,CAAC,EAAE;YACtB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACrB;IACH,CAAC,CAAC,CAAC;IAEH,6EAA6E;IAC7E,4BAA4B;IAC5B,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAClD,MAAM;SACH,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;QAChB,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAE1D,OAAO,kBAAkB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;IAEF,oBAAoB;IACpB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAEnD,OAAO,kBAAkB,CAAC,aAAa,CAAC,CAAC;AAC3C,CAAC;AA/BD,0DA+BC;AAED,SAAgB,gBAAgB,CAC9B,YAAoB,EACpB,QAAkB,EAClB,SAAmB;IAEnB,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACjC,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAEnC,MAAM,aAAa,GAAG,EAAE,CAAC;IAEzB,gCAAgC;IAChC,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE;QAC3B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,oCAAoC;IACpC,IAAI,QAAQ,IAAI,SAAS,EAAE;QACzB,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACxB;IAED,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,QAAQ,CAAC,EAAE;QACxD,kCAAkC;QAClC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACzB;IAED,gCAAgC;IAChC,aAAa,CAAC,IAAI,CAAC,IAAA,oBAAO,EAAC,mBAAmB,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;IAEzE,iCAAiC;IACjC,aAAa,CAAC,IAAI,CAAC,IAAA,oBAAO,EAAC,mBAAmB,EAAE,YAAY,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAE1E,gBAAgB;IAChB,aAAa,CAAC,IAAI,CAAC,IAAA,oBAAO,EAAC,qBAAqB,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;IAErE,wDAAwD;IACxD,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,YAAY,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE;QACxD,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,GAAG,MAAM,EAAE,QAAQ,EAAE,EAAE;YACnE,aAAa,CAAC,IAAI,CAChB,IAAA,oBAAO,EACL,mCAAmC,EACnC,QAAQ,EACR,YAAY,GAAG,QAAQ,GAAG,MAAM,GAAG,CAAC,CACrC,CACF,CAAC;SACH;KACF;IAED,OAAO,kBAAkB,CAAC,aAAa,CAAC,CAAC;AAC3C,CAAC;AAhDD,4CAgDC"}