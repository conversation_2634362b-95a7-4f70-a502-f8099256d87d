# SSH Config File Example (~/.ssh/config)
# 將此內容添加到您的 ~/.ssh/config 文件中

# 新VPS - Tokyo
Host vps-tokyo
    HostName *************
    User root
    Port 22
    # IdentityFile ~/.ssh/id_rsa  # 如果使用密鑰認證

# 舊VPS - 主IP
Host vps-old-main
    HostName ************
    User root
    Port 2222
    # IdentityFile ~/.ssh/id_rsa

# 舊VPS - 次IP
Host vps-old-secondary
    HostName *************
    User root
    Port 2222
    # IdentityFile ~/.ssh/id_rsa

# 使用方法：
# ssh vps-tokyo
# ssh vps-old-main
# ssh vps-old-secondary
