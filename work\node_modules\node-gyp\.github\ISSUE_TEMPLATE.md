<!--
Thank you for reporting an issue!

Remember, this issue tracker is for reporting issues ONLY with node-gyp.

If you have an issue installing a specific module, please file an issue on
that module's issue tracker (`npm issues modulename`). Open issue here only if
you are sure this is an issue with node-gyp, not with the module you are
trying to build.

Fill out the form below. We probably won't investigate an issue that does not
provide the basic information we require.

-->

Please look thru your error log for the string `gyp info using node-gyp@` and if the version number is less than the [current release of node-gyp](https://github.com/nodejs/node-gyp/releases) then __please upgrade__ using the instructions at https://github.com/nodejs/node-gyp/blob/master/docs/Updating-npm-bundled-node-gyp.md and try your command again.

Requests for help with [`node-sass` are very common](https://github.com/nodejs/node-gyp/issues?q=label%3A%22Node+Sass+--%3E+Dart+Sass%22). Please be aware that this package is deprecated, you should seek alternatives and avoid opening new issues about it here.

* **Node Version**: <!-- `node -v` and `npm -v` -->
* **Platform**: <!-- `uname -a` (UNIX), or `systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"` (Windows) -->
* **Compiler**: <!-- `cc -v` (UNIX) or `msbuild /version & cl` (Windows) -->
* **Module**: <!-- what you tried to build/install -->

<details><summary>Verbose output (from npm or node-gyp):</summary>

```
Paste your log here, between the backticks. It can be:
  - npm --verbose output,
  - or contents of npm-debug.log,
  - or output of node-gyp rebuild --verbose.
Include the command you were trying to run.

This should look like this:

>npm --verbose
npm info it worked if it ends with ok
npm verb cli [
npm verb cli   'C:\\...\\node\\13.9.0\\x64\\node.exe',
npm verb cli   'C:\\...\\node\\13.9.0\\x64\\node_modules\\npm\\bin\\npm-cli.js',
npm verb cli   '--verbose'
npm verb cli ]
npm info using npm@6.13.7
npm info using node@v13.9.0

Usage: npm <command>
(...)
```

</details>

<!-- Any further details -->
