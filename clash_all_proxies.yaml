# Clash 完整代理配置文件
# 服務器: ************* (東京)
# 包含所有 8個SS + 8個SOCKS5 = 16個代理

port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: info
external-controller: 127.0.0.1:9090

proxies:
  # ========== Shadowsocks 代理 (8個) ==========
  - name: "Office01-SS"
    type: ss
    server: *************
    port: 8001
    cipher: aes-256-gcm
    password: office01_ss_pass

  - name: "Office02-SS"
    type: ss
    server: *************
    port: 8002
    cipher: aes-256-gcm
    password: office02_ss_pass

  - name: "Office03-SS"
    type: ss
    server: *************
    port: 8003
    cipher: aes-256-gcm
    password: office03_ss_pass

  - name: "Office04-SS"
    type: ss
    server: *************
    port: 8004
    cipher: aes-256-gcm
    password: office04_ss_pass

  - name: "Office05-SS"
    type: ss
    server: *************
    port: 8005
    cipher: aes-256-gcm
    password: office05_ss_pass

  - name: "Office06-SS"
    type: ss
    server: *************
    port: 8006
    cipher: aes-256-gcm
    password: office06_ss_pass

  - name: "Office07-SS"
    type: ss
    server: *************
    port: 8007
    cipher: aes-256-gcm
    password: office07_ss_pass

  - name: "Office08-SS"
    type: ss
    server: *************
    port: 8008
    cipher: aes-256-gcm
    password: office08_ss_pass

  # ========== SOCKS5 代理 (8個) ==========
  - name: "Office01-SOCKS5"
    type: socks5
    server: *************
    port: 7001
    username: office01
    password: office01_s5_pass

  - name: "Office02-SOCKS5"
    type: socks5
    server: *************
    port: 7002
    username: office02
    password: office02_s5_pass

  - name: "Office03-SOCKS5"
    type: socks5
    server: *************
    port: 7003
    username: office03
    password: office03_s5_pass

  - name: "Office04-SOCKS5"
    type: socks5
    server: *************
    port: 7004
    username: office04
    password: office04_s5_pass

  - name: "Office05-SOCKS5"
    type: socks5
    server: *************
    port: 7005
    username: office05
    password: office05_s5_pass

  - name: "Office06-SOCKS5"
    type: socks5
    server: *************
    port: 7006
    username: office06
    password: office06_s5_pass

  - name: "Office07-SOCKS5"
    type: socks5
    server: *************
    port: 7007
    username: office07
    password: office07_s5_pass

  - name: "Office08-SOCKS5"
    type: socks5
    server: *************
    port: 7008
    username: office08
    password: office08_s5_pass

proxy-groups:
  # 主要代理選擇組
  - name: "🚀 代理選擇"
    type: select
    proxies:
      - "♻️ 自動選擇-SS"
      - "♻️ 自動選擇-SOCKS5"
      - "🔄 故障轉移-SS"
      - "🔄 故障轉移-SOCKS5"
      - "⚖️ 負載均衡-SS"
      - "⚖️ 負載均衡-SOCKS5"
      - "📋 所有SS代理"
      - "📋 所有SOCKS5代理"
      - "DIRECT"

  # SS代理組
  - name: "📋 所有SS代理"
    type: select
    proxies:
      - "Office01-SS"
      - "Office02-SS"
      - "Office03-SS"
      - "Office04-SS"
      - "Office05-SS"
      - "Office06-SS"
      - "Office07-SS"
      - "Office08-SS"

  # SOCKS5代理組
  - name: "📋 所有SOCKS5代理"
    type: select
    proxies:
      - "Office01-SOCKS5"
      - "Office02-SOCKS5"
      - "Office03-SOCKS5"
      - "Office04-SOCKS5"
      - "Office05-SOCKS5"
      - "Office06-SOCKS5"
      - "Office07-SOCKS5"
      - "Office08-SOCKS5"

  # SS自動選擇
  - name: "♻️ 自動選擇-SS"
    type: url-test
    proxies:
      - "Office01-SS"
      - "Office02-SS"
      - "Office03-SS"
      - "Office04-SS"
      - "Office05-SS"
      - "Office06-SS"
      - "Office07-SS"
      - "Office08-SS"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # SOCKS5自動選擇
  - name: "♻️ 自動選擇-SOCKS5"
    type: url-test
    proxies:
      - "Office01-SOCKS5"
      - "Office02-SOCKS5"
      - "Office03-SOCKS5"
      - "Office04-SOCKS5"
      - "Office05-SOCKS5"
      - "Office06-SOCKS5"
      - "Office07-SOCKS5"
      - "Office08-SOCKS5"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # SS故障轉移
  - name: "🔄 故障轉移-SS"
    type: fallback
    proxies:
      - "Office01-SS"
      - "Office02-SS"
      - "Office03-SS"
      - "Office04-SS"
      - "Office05-SS"
      - "Office06-SS"
      - "Office07-SS"
      - "Office08-SS"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  # SOCKS5故障轉移
  - name: "🔄 故障轉移-SOCKS5"
    type: fallback
    proxies:
      - "Office01-SOCKS5"
      - "Office02-SOCKS5"
      - "Office03-SOCKS5"
      - "Office04-SOCKS5"
      - "Office05-SOCKS5"
      - "Office06-SOCKS5"
      - "Office07-SOCKS5"
      - "Office08-SOCKS5"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  # SS負載均衡
  - name: "⚖️ 負載均衡-SS"
    type: load-balance
    proxies:
      - "Office01-SS"
      - "Office02-SS"
      - "Office03-SS"
      - "Office04-SS"
      - "Office05-SS"
      - "Office06-SS"
      - "Office07-SS"
      - "Office08-SS"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  # SOCKS5負載均衡
  - name: "⚖️ 負載均衡-SOCKS5"
    type: load-balance
    proxies:
      - "Office01-SOCKS5"
      - "Office02-SOCKS5"
      - "Office03-SOCKS5"
      - "Office04-SOCKS5"
      - "Office05-SOCKS5"
      - "Office06-SOCKS5"
      - "Office07-SOCKS5"
      - "Office08-SOCKS5"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

rules:
  # 常見被牆網站
  - DOMAIN-SUFFIX,google.com,🚀 代理選擇
  - DOMAIN-SUFFIX,googleapis.com,🚀 代理選擇
  - DOMAIN-SUFFIX,youtube.com,🚀 代理選擇
  - DOMAIN-SUFFIX,ytimg.com,🚀 代理選擇
  - DOMAIN-SUFFIX,facebook.com,🚀 代理選擇
  - DOMAIN-SUFFIX,twitter.com,🚀 代理選擇
  - DOMAIN-SUFFIX,instagram.com,🚀 代理選擇
  - DOMAIN-SUFFIX,telegram.org,🚀 代理選擇
  - DOMAIN-SUFFIX,github.com,🚀 代理選擇
  - DOMAIN-SUFFIX,stackoverflow.com,🚀 代理選擇
  - DOMAIN-SUFFIX,reddit.com,🚀 代理選擇
  - DOMAIN-SUFFIX,discord.com,🚀 代理選擇
  - DOMAIN-SUFFIX,netflix.com,🚀 代理選擇
  - DOMAIN-SUFFIX,openai.com,🚀 代理選擇
  - DOMAIN-SUFFIX,chatgpt.com,🚀 代理選擇
  
  # 關鍵字匹配
  - DOMAIN-KEYWORD,google,🚀 代理選擇
  - DOMAIN-KEYWORD,youtube,🚀 代理選擇
  - DOMAIN-KEYWORD,facebook,🚀 代理選擇
  - DOMAIN-KEYWORD,twitter,🚀 代理選擇
  - DOMAIN-KEYWORD,telegram,🚀 代理選擇
  - DOMAIN-KEYWORD,github,🚀 代理選擇
  
  # 中國大陸直連
  - GEOIP,CN,DIRECT
  
  # 局域網直連
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,*********/8,DIRECT
  
  # 其他流量走代理
  - MATCH,🚀 代理選擇
