# SSH密钥配置指南

## 🔐 SSH密钥认证设置

### 步骤1: 生成SSH密钥对（在本地执行）

```bash
# 生成RSA密钥对（推荐4096位）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 或者生成ED25519密钥（更安全，更快）
ssh-keygen -t ed25519 -C "<EMAIL>"
```

**交互过程：**
```
Generating public/private rsa key pair.
Enter file in which to save the key (/home/<USER>/.ssh/id_rsa): /home/<USER>/.ssh/vps_srv211241698
Enter passphrase (empty for no passphrase): [输入密码短语，可选]
Enter same passphrase again: [再次输入]
```

### 步骤2: 将公钥复制到VPS服务器

```bash
# 方法1: 使用ssh-copy-id（推荐）
ssh-copy-id -i ~/.ssh/vps_srv211241698.pub -p 2222 root@************

# 方法2: 手动复制
cat ~/.ssh/vps_srv211241698.pub | ssh -p 2222 root@************ "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### 步骤3: 配置本地SSH客户端

创建或编辑 `~/.ssh/config` 文件：

```bash
# VPS服务器配置
Host vps-srv211241698
    HostName ************
    Port 2222
    User root
    IdentityFile ~/.ssh/vps_srv211241698
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 使用主机名的备用配置
Host vps-srv211241698-hostname
    HostName srv211241698.host
    Port 2222
    User root
    IdentityFile ~/.ssh/vps_srv211241698
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 使用辅助IP的配置
Host vps-srv211241698-secondary
    HostName *************
    Port 2222
    User root
    IdentityFile ~/.ssh/vps_srv211241698
    ServerAliveInterval 60
    ServerAliveCountMax 3
```

### 步骤4: 测试SSH密钥连接

```bash
# 使用配置的主机名连接
ssh vps-srv211241698

# 直接使用密钥文件连接
ssh -i ~/.ssh/vps_srv211241698 -p 2222 root@************
```

### 步骤5: 禁用密码认证（可选，提高安全性）

连接到VPS后，编辑SSH配置：

```bash
# 备份原配置
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 编辑SSH配置
nano /etc/ssh/sshd_config
```

修改以下配置项：
```
# 禁用密码认证
PasswordAuthentication no

# 禁用空密码
PermitEmptyPasswords no

# 启用公钥认证
PubkeyAuthentication yes

# 指定授权密钥文件
AuthorizedKeysFile .ssh/authorized_keys

# 禁用root密码登录（保留密钥登录）
PermitRootLogin prohibit-password
```

重启SSH服务：
```bash
systemctl restart sshd
```

## 🛡️ 安全最佳实践

### 密钥管理
1. **使用密码短语保护私钥**
2. **定期轮换SSH密钥**
3. **不同服务器使用不同密钥**
4. **备份私钥到安全位置**

### 权限设置
```bash
# 设置正确的文件权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
chmod 600 ~/.ssh/vps_srv211241698
chmod 644 ~/.ssh/vps_srv211241698.pub
```

### SSH配置优化
```bash
# 在服务器端 /etc/ssh/sshd_config 中添加
ClientAliveInterval 300
ClientAliveCountMax 2
MaxAuthTries 3
MaxSessions 2
```

## 🔧 故障排除

### 常见问题

**1. 权限问题**
```bash
# 检查文件权限
ls -la ~/.ssh/

# 修复权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/*
chmod 644 ~/.ssh/*.pub
```

**2. 连接被拒绝**
```bash
# 检查SSH服务状态
systemctl status sshd

# 查看SSH日志
tail -f /var/log/auth.log
```

**3. 密钥不被接受**
```bash
# 详细连接日志
ssh -vvv -i ~/.ssh/vps_srv211241698 -p 2222 root@************

# 检查authorized_keys文件
cat ~/.ssh/authorized_keys
```

## 📋 快速命令参考

### 生成密钥
```bash
# RSA 4096位
ssh-keygen -t rsa -b 4096 -f ~/.ssh/vps_srv211241698

# ED25519（推荐）
ssh-keygen -t ed25519 -f ~/.ssh/vps_srv211241698
```

### 复制公钥
```bash
ssh-copy-id -i ~/.ssh/vps_srv211241698.pub -p 2222 root@************
```

### 连接测试
```bash
ssh -i ~/.ssh/vps_srv211241698 -p 2222 root@************
```

### 配置文件连接
```bash
ssh vps-srv211241698
```

---
**注意**: 设置SSH密钥后，请确保私钥文件安全保存，并考虑创建备份。
