# 東京VPS代理服務配置總結

## 📊 服務器信息
- **服務器IP**: *************
- **位置**: 東京
- **主機名**: srv461468751.host
- **總代理數**: 16個 (8個SS + 8個SOCKS5)

## 🔐 Shadowsocks 代理配置 (8個)

| 辦公室 | 服務器 | 端口 | 密碼 | 加密方式 |
|--------|--------|------|------|----------|
| Office01 | ************* | 8001 | office01_ss_pass | aes-256-gcm |
| Office02 | ************* | 8002 | office02_ss_pass | aes-256-gcm |
| Office03 | ************* | 8003 | office03_ss_pass | aes-256-gcm |
| Office04 | ************* | 8004 | office04_ss_pass | aes-256-gcm |
| Office05 | ************* | 8005 | office05_ss_pass | aes-256-gcm |
| Office06 | ************* | 8006 | office06_ss_pass | aes-256-gcm |
| Office07 | ************* | 8007 | office07_ss_pass | aes-256-gcm |
| Office08 | ************* | 8008 | office08_ss_pass | aes-256-gcm |

## 🌐 SOCKS5 代理配置 (8個)

| 辦公室 | 服務器 | 端口 | 用戶名 | 密碼 |
|--------|--------|------|--------|------|
| Office01 | ************* | 7001 | office01 | office01_s5_pass |
| Office02 | ************* | 7002 | office02 | office02_s5_pass |
| Office03 | ************* | 7003 | office03 | office03_s5_pass |
| Office04 | ************* | 7004 | office04 | office04_s5_pass |
| Office05 | ************* | 7005 | office05 | office05_s5_pass |
| Office06 | ************* | 7006 | office06 | office06_s5_pass |
| Office07 | ************* | 7007 | office07 | office07_s5_pass |
| Office08 | ************* | 7008 | office08 | office08_s5_pass |

## 🚀 Clash 配置特點

### 代理組功能
- **🚀 代理選擇**: 主要選擇組，包含所有代理選項
- **📋 所有SS代理**: 手動選擇任意SS代理
- **📋 所有SOCKS5代理**: 手動選擇任意SOCKS5代理
- **♻️ 自動選擇**: 自動測速選擇最快代理
- **🔄 故障轉移**: 自動切換故障代理
- **⚖️ 負載均衡**: 流量負載分散

### 路由規則
- **被牆網站**: Google、YouTube、Facebook、Twitter等自動走代理
- **國內網站**: 中國大陸IP直連
- **局域網**: 內網流量直連
- **其他流量**: 默認走代理

## 📁 文件說明
- **clash.yaml**: 完整的Clash配置文件，包含所有16個代理
- **proxy_summary.md**: 本配置總結文件

## 🔧 使用方法
1. 將 `clash.yaml` 導入到Clash客戶端
2. 根據需要選擇對應的代理組
3. 可以為不同辦公室分配專屬的代理配置

## 📋 管理命令 (VPS端)
```bash
# 檢查所有服務狀態
systemctl status shadowsocks-800{1..8} socks5-700{1..8}

# 重啟所有服務
systemctl restart shadowsocks-800{1..8} socks5-700{1..8}

# 檢查端口監聽
ss -tlnp | grep -E "(800[1-8]|700[1-8])"
```

## ✅ 部署狀態
- ✅ 所有16個代理服務已部署並運行
- ✅ 防火牆端口已開放
- ✅ Clash配置文件已生成
- ✅ 可以開始使用

---
*配置完成時間: 2025-07-16*
*服務器位置: 東京*
