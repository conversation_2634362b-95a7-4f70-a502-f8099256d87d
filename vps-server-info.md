# VPS服务器信息与操作指南

## 🖥️ 服务器基本信息

### 连接信息
- **主机名**: srv211241698.host
- **SSH端口**: 2222 (非标准端口，增强安全性)
- **用户名**: root
- **密码**: llB81594jc324AN_9t9_yw

### IP地址配置
- **主IP地址**: ************
- **辅助IP地址**: *************
- **子网掩码**: *************
- **网关**: ***********

### 系统信息
- **操作系统**: Ubuntu 22.04.5 LTS
- **内核版本**: 5.15.0-143-generic
- **架构**: x86_64
- **托管商**: UltaHost

## 🔐 SSH连接方法

### 基本连接命令
```bash
# 使用主机名连接
ssh -p 2222 <EMAIL>

# 使用IP地址连接（推荐）
ssh -p 2222 root@************
```

### SSH密钥配置（待生成）
```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥复制到服务器
ssh-copy-id -p 2222 root@************

# 配置本地SSH客户端
# 编辑 ~/.ssh/config 文件
Host vps-server
    HostName ************
    Port 2222
    User root
    IdentityFile ~/.ssh/id_rsa
```

## 🛡️ 安全状况

### 最近攻击记录（最近8小时）
- **总攻击次数**: 13次失败的密码尝试
- **主要攻击IP**: 
  - ************** (2次)
  - ************** (2次)
  - ************** (2次)
  - 104.28.228.x网段 (6次)

### 安全建议
1. 已使用非标准SSH端口2222 ✅
2. 建议配置SSH密钥认证
3. 考虑安装fail2ban防暴力破解
4. 定期检查系统日志

## 🌐 代理服务配置

### 已部署的代理服务

#### Shadowsocks服务器
- **端口**: 28889, 30001, 30002, 30003
- **分布**: 主IP和辅助IP分别部署

#### SOCKS5服务器  
- **端口**: 7777, 8001, 8002, 8003
- **认证**: 各有特定的用户名密码

### IP分离策略
- **主IP (************)**: 部分代理服务
- **辅助IP (*************)**: 其他代理服务
- **目的**: 分散流量，提高稳定性

## 📋 VPS操作逻辑与最佳实践

### 🔍 正确的服务测试方法

#### ❌ 错误方法
```bash
# 不要用telnet测试代理服务
telnet localhost 8080  # 这样测试代理是无效的
```

#### ✅ 正确方法

**1. 检查端口监听状态**
```bash
# 检查特定端口是否在监听
netstat -tlnp | grep 端口号

# 检查所有监听端口
netstat -tlnp

# 使用ss命令（更现代）
ss -tlnp | grep 端口号
```

**2. 检查进程状态**
```bash
# 检查特定服务进程
ps aux | grep 服务名

# 检查所有进程
ps aux

# 使用htop查看实时进程
htop
```

**3. 使用实际代理客户端测试**
```bash
# 对于Shadowsocks
# 使用ss-local或Clash等客户端连接测试

# 对于SOCKS5代理
curl --socks5 127.0.0.1:端口号 http://httpbin.org/ip

# 对于HTTP代理
curl --proxy http://127.0.0.1:端口号 http://httpbin.org/ip
```

### 🔧 系统维护命令

#### 系统状态检查
```bash
# 系统负载
uptime
top
htop

# 内存使用
free -h

# 磁盘使用
df -h

# 网络连接
netstat -an
ss -an
```

#### 日志检查
```bash
# SSH登录日志
last
lastlog

# 认证失败日志
grep "Failed password" /var/log/auth.log

# 系统日志
journalctl -f
tail -f /var/log/syslog
```

#### 服务管理
```bash
# 查看服务状态
systemctl status 服务名

# 启动/停止/重启服务
systemctl start 服务名
systemctl stop 服务名
systemctl restart 服务名

# 开机自启
systemctl enable 服务名
systemctl disable 服务名
```

### 🚀 代理服务管理

#### 服务部署检查清单
1. **端口分配确认**
   - 检查端口是否冲突
   - 确认防火墙规则
   
2. **进程启动验证**
   - 使用ps aux检查进程
   - 使用netstat检查端口监听
   
3. **功能测试**
   - 使用实际客户端测试连接
   - 验证代理功能正常
   
4. **性能监控**
   - 监控CPU和内存使用
   - 检查网络流量

#### 故障排除流程
1. **检查进程是否运行**
2. **检查端口是否监听**
3. **检查防火墙设置**
4. **查看服务日志**
5. **测试网络连通性**

### 📊 监控与维护

#### 定期检查项目
- [ ] 系统资源使用情况
- [ ] 代理服务运行状态
- [ ] SSH攻击日志
- [ ] 系统更新状态
- [ ] 磁盘空间使用

#### 安全维护
- [ ] 定期更换密码
- [ ] 配置SSH密钥认证
- [ ] 安装安全防护软件
- [ ] 监控异常登录
- [ ] 备份重要配置

## 📝 常用命令速查

### 快速状态检查
```bash
# 一键检查系统状态
echo "=== 系统负载 ===" && uptime && \
echo "=== 内存使用 ===" && free -h && \
echo "=== 磁盘使用 ===" && df -h && \
echo "=== 网络连接 ===" && netstat -tlnp
```

### 代理服务快速检查
```bash
# 检查所有代理端口
for port in 28889 30001 30002 30003 7777 8001 8002 8003; do
    echo "检查端口 $port:"
    netstat -tlnp | grep $port
done
```

---
**最后更新**: 2025-07-15
**维护人员**: [您的姓名]
