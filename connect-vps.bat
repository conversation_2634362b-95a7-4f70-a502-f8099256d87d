@echo off
echo 選擇要連接的VPS:
echo 1. 新VPS - Tokyo (*************)
echo 2. 舊VPS - 主IP (************)
echo 3. 舊VPS - 次IP (*************)
echo.

set /p choice=請輸入選項 (1-3): 

if "%choice%"=="1" (
    echo 連接到新VPS - Tokyo...
    ssh root@*************
) else if "%choice%"=="2" (
    echo 連接到舊VPS - 主IP...
    ssh -p 2222 root@************
) else if "%choice%"=="3" (
    echo 連接到舊VPS - 次IP...
    ssh -p 2222 root@*************
) else (
    echo 無效選項，請重新運行腳本。
)
