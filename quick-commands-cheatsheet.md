# VPS快速操作命令备忘录

## 🚀 快速连接

```bash
# SSH连接（密码方式）
ssh root@*************

# SSH连接（密钥方式，配置后）
ssh vps-srv461468751
```

## 🔍 系统状态快速检查

### 一键系统状态
```bash
echo "=== 系统概览 $(date) ===" && \
echo "负载: $(uptime | awk -F'load average:' '{print $2}')" && \
echo "内存: $(free -h | grep Mem | awk '{print $3"/"$2" ("$3/$2*100"%)"}')" && \
echo "磁盘: $(df -h / | awk 'NR==2{print $3"/"$2" ("$5")"}')" && \
echo "进程数: $(ps aux | wc -l)" && \
echo "网络连接: $(netstat -an | grep ESTABLISHED | wc -l)"
```

### 快速资源检查
```bash
# CPU和内存
top -bn1 | head -5

# 磁盘空间
df -h

# 网络连接
netstat -tlnp | head -10
```

## 🌐 代理服务快速检查

### 检查所有代理端口
```bash
for port in 28889 30001 30002 30003 7777 8001 8002 8003; do
    echo -n "端口 $port: "
    netstat -tlnp | grep ":$port " > /dev/null && echo "✅ 监听中" || echo "❌ 未监听"
done
```

### 检查代理进程
```bash
ps aux | grep -E "(shadowsocks|socks|dante|3proxy)" | grep -v grep
```

### 快速代理测试
```bash
# 测试SOCKS5代理（需要替换实际的用户名密码）
curl --connect-timeout 5 --socks5 user1:pass1@*************:7777 http://httpbin.org/ip
```

## 🛡️ 安全检查命令

### SSH攻击检查
```bash
# 最近的失败登录
grep "Failed password" /var/log/auth.log | tail -10

# 最近8小时的攻击
awk '/Jul 15 [0-9][0-9]:/ && /Failed password/' /var/log/auth.log

# 攻击IP统计
grep "Failed password" /var/log/auth.log | awk '{print $11}' | sort | uniq -c | sort -nr | head -10
```

### 系统安全状态
```bash
# 当前登录用户
who

# 最近登录记录
last | head -10

# 检查可疑进程
ps aux | grep -v "^\[" | sort -k3 -nr | head -10
```

## 🔧 服务管理命令

### 系统服务
```bash
# 查看服务状态
systemctl status sshd
systemctl status shadowsocks-libev
systemctl status danted

# 重启服务
systemctl restart sshd
systemctl restart shadowsocks-libev
systemctl restart danted
```

### 进程管理
```bash
# 查找进程
pgrep -f shadowsocks
pgrep -f socks

# 杀死进程
pkill -f shadowsocks
pkill -f socks
```

## 📊 监控命令

### 实时监控
```bash
# 实时系统状态
htop

# 实时网络连接
watch -n 1 'netstat -tlnp'

# 实时日志
tail -f /var/log/syslog
tail -f /var/log/auth.log
```

### 流量监控
```bash
# 网络接口流量
cat /proc/net/dev

# 实时流量监控
iftop
nethogs
```

## 🗂️ 文件操作

### 配置文件位置
```bash
# SSH配置
/etc/ssh/sshd_config

# Shadowsocks配置
/etc/shadowsocks-libev/config.json

# SOCKS5配置
/etc/danted.conf
/etc/3proxy/3proxy.cfg
```

### 快速备份
```bash
# 备份重要配置
tar -czf config-backup-$(date +%Y%m%d).tar.gz \
    /etc/ssh/sshd_config \
    /etc/shadowsocks-libev/ \
    /etc/danted.conf \
    /etc/3proxy/ 2>/dev/null
```

### 日志文件
```bash
# 重要日志位置
/var/log/auth.log      # 认证日志
/var/log/syslog        # 系统日志
/var/log/daemon.log    # 守护进程日志
```

## 🚨 紧急处理命令

### 紧急停止服务
```bash
# 停止所有代理服务
systemctl stop shadowsocks-libev
systemctl stop danted
systemctl stop 3proxy

# 或者强制杀死进程
pkill -9 -f shadowsocks
pkill -9 -f socks
pkill -9 -f dante
pkill -9 -f 3proxy
```

### 防火墙紧急操作
```bash
# 查看防火墙状态
ufw status

# 临时开放端口
ufw allow 2222/tcp

# 临时关闭端口
ufw deny 28889/tcp
```

### 系统紧急信息
```bash
# 系统负载过高时查看
ps aux --sort=-%cpu | head -10
ps aux --sort=-%mem | head -10

# 磁盘空间不足时
du -sh /* | sort -hr | head -10
find /var/log -name "*.log" -size +100M
```

## 📋 定期维护命令

### 每日检查脚本
```bash
#!/bin/bash
# daily-check.sh
echo "=== 每日检查报告 $(date) ==="

# 系统状态
echo "1. 系统负载: $(uptime | awk -F'load average:' '{print $2}')"
echo "2. 内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "3. 磁盘使用: $(df -h / | awk 'NR==2{print $5}')"

# 代理服务状态
echo "4. 代理端口检查:"
for port in 28889 30001 30002 30003 7777 8001 8002 8003; do
    netstat -tlnp | grep ":$port " > /dev/null && echo "   ✅ $port" || echo "   ❌ $port"
done

# 安全检查
echo "5. 今日SSH攻击: $(grep "$(date '+%b %d')" /var/log/auth.log | grep "Failed password" | wc -l) 次"

# 系统更新
echo "6. 可用更新: $(apt list --upgradable 2>/dev/null | wc -l) 个"
```

### 清理命令
```bash
# 清理日志
journalctl --vacuum-time=7d

# 清理临时文件
rm -rf /tmp/*
rm -rf /var/tmp/*

# 清理包缓存
apt autoremove
apt autoclean
```

## 🔗 有用的一行命令

```bash
# 检查端口占用
lsof -i :端口号

# 查看进程树
pstree -p

# 检查网络连接数
netstat -an | awk '/^tcp/ {print $6}' | sort | uniq -c

# 查看最占CPU的进程
ps aux | sort -k3 -nr | head -5

# 查看最占内存的进程
ps aux | sort -k4 -nr | head -5

# 检查磁盘IO
iostat -x 1

# 查看网络接口状态
ip addr show

# 检查路由表
ip route show

# 查看DNS设置
cat /etc/resolv.conf
```

---
**使用提示**: 
- 将常用命令保存为别名或脚本
- 定期执行检查命令确保系统正常
- 紧急情况下优先使用紧急处理命令
