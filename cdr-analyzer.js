const fs = require('fs');
const path = require('path');

class CDRAnalyzer {
    constructor(csvFilePath) {
        this.csvFilePath = csvFilePath;
        this.data = [];
        this.stats = {};
    }

    // 讀取和解析CSV文件
    loadData() {
        try {
            const content = fs.readFileSync(this.csvFilePath, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim());
            
            // 跳過標題行
            const dataLines = lines.slice(1);
            
            this.data = dataLines.map((line, index) => {
                try {
                    // 處理CSV格式，移除Excel格式的等號和引號
                    const cleanLine = line.replace(/="/g, '').replace(/",/g, ',').replace(/"/g, '');
                    const fields = cleanLine.split(',');
                    
                    if (fields.length >= 14) {
                        return {
                            rowNumber: index + 2,
                            agentId: fields[0]?.trim() || '',
                            task: fields[1]?.trim() || '',
                            businessType: fields[2]?.trim() || '',
                            createTime: fields[3]?.trim() || '',
                            startTime: fields[4]?.trim() || '',
                            endTime: fields[5]?.trim() || '',
                            callerNumber: fields[6]?.trim() || '',
                            calleeNumber: fields[7]?.trim() || '',
                            duration: parseInt(fields[8]) || 0,
                            cost: parseFloat(fields[9]) || 0,
                            hangupReason: fields[10]?.trim() || '',
                            callResult: fields[11]?.trim() || '',
                            type: fields[12]?.trim() || '',
                            ringDuration: parseInt(fields[13]) || 0,
                            keyInfo: fields[14]?.trim() || '',
                            province: fields[15]?.trim() || '',
                            city: fields[16]?.trim() || '',
                            operator: fields[17]?.trim() || '',
                            originalError: fields[18]?.trim() || '',
                            chineseError: fields[19]?.trim() || ''
                        };
                    }
                    return null;
                } catch (error) {
                    console.warn(`解析第 ${index + 2} 行時出錯:`, error.message);
                    return null;
                }
            }).filter(record => record !== null);

            console.log(`成功載入 ${this.data.length} 條CDR記錄`);
            return true;
        } catch (error) {
            console.error('讀取CDR文件失敗:', error.message);
            return false;
        }
    }

    // 生成統計分析
    generateStats() {
        if (this.data.length === 0) {
            console.log('沒有數據可分析');
            return;
        }

        // 基本統計
        this.stats.totalCalls = this.data.length;
        this.stats.totalDuration = this.data.reduce((sum, record) => sum + record.duration, 0);
        this.stats.totalCost = this.data.reduce((sum, record) => sum + record.cost, 0);
        this.stats.averageDuration = Math.round(this.stats.totalDuration / this.stats.totalCalls);
        this.stats.averageCost = (this.stats.totalCost / this.stats.totalCalls).toFixed(4);

        // 時間範圍
        const times = this.data.map(record => new Date(record.startTime)).filter(date => !isNaN(date));
        if (times.length > 0) {
            this.stats.startDate = new Date(Math.min(...times)).toLocaleString('zh-TW');
            this.stats.endDate = new Date(Math.max(...times)).toLocaleString('zh-TW');
        }

        // 坐席統計
        this.stats.agentStats = {};
        this.data.forEach(record => {
            const agent = record.agentId;
            if (!this.stats.agentStats[agent]) {
                this.stats.agentStats[agent] = {
                    calls: 0,
                    duration: 0,
                    cost: 0
                };
            }
            this.stats.agentStats[agent].calls++;
            this.stats.agentStats[agent].duration += record.duration;
            this.stats.agentStats[agent].cost += record.cost;
        });

        // 呼叫結果統計
        this.stats.callResults = {};
        this.data.forEach(record => {
            const result = record.callResult;
            this.stats.callResults[result] = (this.stats.callResults[result] || 0) + 1;
        });

        // 掛斷原因統計
        this.stats.hangupReasons = {};
        this.data.forEach(record => {
            const reason = record.hangupReason;
            this.stats.hangupReasons[reason] = (this.stats.hangupReasons[reason] || 0) + 1;
        });

        // 業務類型統計
        this.stats.businessTypes = {};
        this.data.forEach(record => {
            const type = record.businessType;
            this.stats.businessTypes[type] = (this.stats.businessTypes[type] || 0) + 1;
        });

        // 通話時長分布
        this.stats.durationDistribution = {
            '0-30秒': 0,
            '31-60秒': 0,
            '61-120秒': 0,
            '121-300秒': 0,
            '300秒以上': 0
        };

        this.data.forEach(record => {
            const duration = record.duration;
            if (duration <= 30) this.stats.durationDistribution['0-30秒']++;
            else if (duration <= 60) this.stats.durationDistribution['31-60秒']++;
            else if (duration <= 120) this.stats.durationDistribution['61-120秒']++;
            else if (duration <= 300) this.stats.durationDistribution['121-300秒']++;
            else this.stats.durationDistribution['300秒以上']++;
        });
    }

    // 生成報告
    generateReport() {
        this.generateStats();
        
        let report = '\n=== CDR 通話詳細記錄分析報告 ===\n\n';
        
        // 基本統計
        report += '📊 基本統計信息\n';
        report += `總通話數量: ${this.stats.totalCalls} 通\n`;
        report += `總通話時長: ${Math.floor(this.stats.totalDuration / 60)} 分 ${this.stats.totalDuration % 60} 秒\n`;
        report += `總通話費用: ${this.stats.totalCost.toFixed(2)} 元\n`;
        report += `平均通話時長: ${this.stats.averageDuration} 秒\n`;
        report += `平均通話費用: ${this.stats.averageCost} 元\n`;
        
        if (this.stats.startDate && this.stats.endDate) {
            report += `時間範圍: ${this.stats.startDate} ~ ${this.stats.endDate}\n`;
        }
        
        // 坐席統計
        report += '\n👥 坐席統計 (按通話數量排序)\n';
        const sortedAgents = Object.entries(this.stats.agentStats)
            .sort(([,a], [,b]) => b.calls - a.calls);
        
        sortedAgents.forEach(([agent, stats]) => {
            const avgDuration = Math.round(stats.duration / stats.calls);
            report += `${agent}: ${stats.calls}通, ${Math.floor(stats.duration/60)}分${stats.duration%60}秒, ${stats.cost.toFixed(2)}元 (平均${avgDuration}秒/通)\n`;
        });
        
        // 呼叫結果統計
        report += '\n📞 呼叫結果統計\n';
        Object.entries(this.stats.callResults)
            .sort(([,a], [,b]) => b - a)
            .forEach(([result, count]) => {
                const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1);
                report += `${result}: ${count}通 (${percentage}%)\n`;
            });
        
        // 掛斷原因統計
        report += '\n📴 掛斷原因統計\n';
        Object.entries(this.stats.hangupReasons)
            .sort(([,a], [,b]) => b - a)
            .forEach(([reason, count]) => {
                const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1);
                report += `${reason}: ${count}通 (${percentage}%)\n`;
            });
        
        // 業務類型統計
        report += '\n💼 業務類型統計\n';
        Object.entries(this.stats.businessTypes)
            .sort(([,a], [,b]) => b - a)
            .forEach(([type, count]) => {
                const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1);
                report += `${type}: ${count}通 (${percentage}%)\n`;
            });
        
        // 通話時長分布
        report += '\n⏱️ 通話時長分布\n';
        Object.entries(this.stats.durationDistribution).forEach(([range, count]) => {
            const percentage = ((count / this.stats.totalCalls) * 100).toFixed(1);
            report += `${range}: ${count}通 (${percentage}%)\n`;
        });
        
        return report;
    }

    // 導出清理後的數據
    exportCleanData(outputPath) {
        if (this.data.length === 0) {
            console.log('沒有數據可導出');
            return false;
        }

        try {
            // CSV格式
            const csvHeader = 'rowNumber,agentId,task,businessType,createTime,startTime,endTime,callerNumber,calleeNumber,duration,cost,hangupReason,callResult,type,ringDuration,keyInfo,province,city,operator,originalError,chineseError\n';
            const csvContent = this.data.map(record => 
                Object.values(record).map(value => `"${value}"`).join(',')
            ).join('\n');
            
            fs.writeFileSync(outputPath, csvHeader + csvContent, 'utf-8');
            console.log(`清理後的數據已導出到: ${outputPath}`);
            return true;
        } catch (error) {
            console.error('導出數據失敗:', error.message);
            return false;
        }
    }

    // 導出JSON格式
    exportJSON(outputPath) {
        if (this.data.length === 0) {
            console.log('沒有數據可導出');
            return false;
        }

        try {
            const jsonData = {
                metadata: {
                    totalRecords: this.data.length,
                    exportTime: new Date().toISOString(),
                    source: this.csvFilePath
                },
                statistics: this.stats,
                records: this.data
            };

            fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2), 'utf-8');
            console.log(`JSON數據已導出到: ${outputPath}`);
            return true;
        } catch (error) {
            console.error('導出JSON失敗:', error.message);
            return false;
        }
    }
}

// 主程序
function main() {
    // 檢查命令行參數
    const csvFile = process.argv[2] || 'CDR-20250725161920.csv';
    
    if (!fs.existsSync(csvFile)) {
        console.error(`找不到CDR文件: ${csvFile}`);
        return;
    }

    const analyzer = new CDRAnalyzer(csvFile);
    
    console.log('開始分析CDR數據...');
    
    if (analyzer.loadData()) {
        // 生成分析報告
        const report = analyzer.generateReport();
        console.log(report);
        
        // 保存報告到文件
        fs.writeFileSync('CDR-分析報告.txt', report, 'utf-8');
        console.log('\n📄 分析報告已保存到: CDR-分析報告.txt');
        
        // 導出清理後的數據
        analyzer.exportCleanData('CDR-清理後數據.csv');
        
        // 導出JSON格式
        analyzer.exportJSON('CDR-完整數據.json');
        
        console.log('\n✅ CDR數據分析完成！');
    } else {
        console.error('❌ CDR數據載入失敗');
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    main();
}

module.exports = CDRAnalyzer;
